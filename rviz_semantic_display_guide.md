# RViz语义检测显示配置指南

## 🎯 目标
在RViz中显示真实语义检测的彩色标记球体：
- 🔴 红色球体: 人 (person)
- 🟢 绿色球体: 椅子 (chair)  
- 🔵 蓝色球体: 桌子 (dining table)
- 🟡 黄色球体: 沙发 (couch)
- 🟣 紫色球体: 电视 (tv)
- 🟦 青色球体: 笔记本电脑 (laptop)

## ✅ 系统状态确认
当前所有组件都在正常运行：
- ✅ YOLO检测: `/darknet_ros/bounding_boxes` (有数据)
- ✅ 真实物体检测: `/real_object_detection/detected_objects` (4个标记)
- ✅ 语义区域: `/real_semantic_mapping/semantic_regions` (2个标记)
- ✅ 图像重映射: 已修复YOLO输入问题

## 🔧 RViz配置步骤

### 1. 基础设置
- **Fixed Frame**: 设置为 `map`
- **Target Frame**: 保持为 `<Fixed Frame>`

### 2. 添加语义检测标记
添加 `MarkerArray` 显示：
- **Display Name**: `Real Object Detection`
- **Topic**: `/real_object_detection/detected_objects`
- **Marker Topic**: `/real_object_detection/detected_objects`
- **Queue Size**: 100

### 3. 添加语义区域标记
添加 `MarkerArray` 显示：
- **Display Name**: `Semantic Regions`
- **Topic**: `/real_semantic_mapping/semantic_regions`
- **Marker Topic**: `/real_semantic_mapping/semantic_regions`
- **Queue Size**: 100

### 4. 添加检测统计信息
添加 `MarkerArray` 显示：
- **Display Name**: `Detection Statistics`
- **Topic**: `/real_object_detection/statistics`
- **Marker Topic**: `/real_object_detection/statistics`
- **Queue Size**: 100

### 5. 添加语义点云（可选）
添加 `PointCloud2` 显示：
- **Display Name**: `Semantic Enhanced Pointcloud`
- **Topic**: `/real_semantic_mapping/enhanced_pointcloud`
- **Size (m)**: 0.01
- **Style**: Points
- **Color Transformer**: RGB8 或 Intensity

## 🎨 预期显示效果

### 主要标记
- **彩色球体**: 检测到的物体位置，颜色对应物体类别
- **文本标签**: 显示物体名称和置信度
- **半透明区域**: 语义影响区域

### 统计信息
- **左上角**: 实时检测统计
- **检测趋势**: 历史检测数据

## 🔍 故障排除

### 如果看不到标记：
1. **检查Fixed Frame**: 确保设置为 `map`
2. **重置视角**: 按 `R` 键重置视角
3. **调整视野**: 手动缩放到原点附近
4. **检查话题**: 确认话题名称正确

### 如果标记位置不对：
1. **坐标系问题**: 检查TF变换
2. **深度数据**: 验证深度图像质量
3. **相机标定**: 确认相机内参正确

### 如果检测不准确：
1. **光照条件**: 确保场景光照充足
2. **物体清晰度**: 确保目标物体清晰可见
3. **YOLO阈值**: 调整置信度阈值

## 📊 实时监控命令

```bash
# 检查YOLO检测频率
rostopic hz /darknet_ros/bounding_boxes

# 检查物体检测输出
rostopic hz /real_object_detection/detected_objects

# 查看检测内容
rostopic echo /real_object_detection/detected_objects --noarr -n 1

# 检查语义区域
rostopic echo /real_semantic_mapping/semantic_regions --noarr -n 1
```

## 🎯 成功标志
当配置正确时，您应该能看到：
- 实时移动的彩色球体标记
- 对应的中文物体名称标签
- 检测置信度数值
- 统计信息文本显示

如果仍有问题，请检查终端输出的错误信息。
