#!/usr/bin/env python3

import rospy
from sensor_msgs.msg import Image, CompressedImage
from cv_bridge import CvBridge
import cv2

class YoloImageRemapper:
    """YOLO图像话题重映射器"""
    
    def __init__(self):
        rospy.init_node('yolo_image_remapper', anonymous=True)
        
        self.bridge = CvBridge()
        
        # 订阅实际的图像话题
        self.image_sub = rospy.Subscriber(
            '/stereo_camera/left/image_rect_color',
            Image,
            self.image_callback
        )
        
        # 发布到YOLO期望的话题
        self.image_pub = rospy.Publisher(
            '/camera/color/image_raw',
            Image,
            queue_size=10
        )
        
        rospy.loginfo("🔄 YOLO图像重映射器已启动")
        rospy.loginfo("📡 从 /stereo_camera/left/image_rect_color 重映射到 /camera/color/image_raw")
        
    def image_callback(self, msg):
        """图像回调函数"""
        try:
            # 直接转发图像消息
            output_msg = Image()
            output_msg.header = msg.header
            output_msg.height = msg.height
            output_msg.width = msg.width
            output_msg.encoding = msg.encoding
            output_msg.is_bigendian = msg.is_bigendian
            output_msg.step = msg.step
            output_msg.data = msg.data
            
            # 发布到YOLO话题
            self.image_pub.publish(output_msg)
            
            rospy.loginfo_throttle(5, f"重映射图像: {msg.width}x{msg.height}, 编码: {msg.encoding}")
            
        except Exception as e:
            rospy.logerr(f"图像重映射失败: {e}")

if __name__ == '__main__':
    try:
        remapper = YoloImageRemapper()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
